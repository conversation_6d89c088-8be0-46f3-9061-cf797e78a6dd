# Руководство по тестированию ленивых табов с пагинацией

## Статус изменений (git status)

**Ветка**: KZID-468

**Измененные файлы**: 8 файлов
**Новые файлы**: 2 файла (включая этот гайд)

Все изменения готовы к коммиту и тестированию.

## Обзор реализации

Была реализована система ленивой загрузки табов с пагинацией для NewWorkGroupTabs. Теперь:

1. **Список табов загружается сразу** при раскрытии строки с вложенной таблицей
2. **Содержимое каждого таба загружается лениво** только при открытии конкретного таба
3. **Каждый таб имеет собственную пагинацию** с сохранением состояния

## Что было изменено

### Измененные файлы (по данным git status):
- `src/features/DataGrid/types.ts` - добавлены типы для ленивых табов
- `src/shared/api/apiInstance.ts` - обновления API
- `src/shared/api/mockHandlers.ts` - добавлены mock handlers для ленивых табов
- `src/widgets/NewWorkGroupTabs/lib/index.ts` - экспорт новых функций
- `src/widgets/NewWorkGroupTabs/store/hooks/useNestedData.ts` - добавлена логика ленивой загрузки
- `src/widgets/NewWorkGroupTabs/types.ts` - новые типы для ленивых табов
- `src/widgets/NewWorkGroupTabs/ui/TabsOrTable/index.tsx` - интеграция ленивых табов
- `src/widgets/NewWorkGroupTabs/ui/index.tsx` - обновление основного компонента

### Новые файлы:
- `src/widgets/NewWorkGroupTabs/lib/tableConfigWithLazyTabs.ts` - конфигурация таблицы для ленивых табов
- `src/widgets/NewWorkGroupTabs/ui/CollapseWithLazyTable/` - компонент для отображения ленивых табов

### Новые типы
- `NestedTabsWithLazyTable` - структура для ленивых табов
- `TabsWithLazyTable` - отдельный таб с ленивой загрузкой и пагинацией
- `CollapseWithLazyTableProps` - пропсы для компонента ленивых табов

### Новые компоненты
- `CollapseWithLazyTable` - компонент для отображения ленивых табов
- `tableConfigWithLazyTabs` - конфигурация таблицы для ленивых табов

### Обновленные хуки
- `useNestedData` - добавлена функция `getLazyTabContent` для загрузки содержимого табов
- Добавлена настройка `lazyNestedTableEndpoints` для определения ленивых эндпойнтов

## Как тестировать

### 1. Настройка эндпойнта
Эндпойнт `apiUrls.workGroup.nestedTabsEndpoints.packageDef` настроен как ленивый.

### 2. Ожидаемое поведение

#### При раскрытии строки:
- Показывается список табов (например, "Файлы в составе пакета")
- Содержимое табов НЕ загружается сразу

#### При открытии таба:
- Показывается спиннер загрузки
- Выполняется запрос к эндпойнту таба (например, `krg3_package_files_def`)
- Загружается первая страница данных
- Показывается таблица с данными и пагинация (если данных больше pageSize)

#### При переключении страниц:
- Выполняется новый запрос с параметром `pageNumber`
- Обновляется содержимое таба
- Состояние пагинации сохраняется для каждого таба отдельно

### 3. Структура запросов

#### Запрос основной таблицы (включает список табов):
```
POST /krg3_input_package_def
{
  cabinetId: "...",
  pageSize: 10,
  pageNumber: 1
}
```

**Ответ содержит список табов в `nestedTable.tabs` с `tableData: null`**

#### Запрос содержимого таба:
```
POST /krg3_package_files_def
{
  cabinetId: "...",
  packageDefId: "...",
  pageSize: 10,
  pageNumber: 1
}
```

### 4. Проверка в DevTools

1. Откройте Network tab в DevTools
2. Раскройте строку с packageDef
3. **НЕТ отдельного запроса для табов** - список табов уже пришел в основном ответе
4. Откройте таб "Файлы в составе пакета"
5. Проверьте, что выполняется запрос к эндпойнту таба (`krg3_package_files_def`)
6. Переключите страницу в пагинации
7. Проверьте, что выполняется новый запрос с обновленным pageNumber

## Возможные проблемы

1. **Эндпойнт не настроен как ленивый** - проверьте `lazyNestedTableEndpoints` в `useNestedData`
2. **Неправильная структура ответа** - убедитесь, что бэкенд возвращает правильную структуру
3. **Ошибки загрузки** - проверьте консоль на наличие ошибок сети или JavaScript

## Требования для бэкенда

### Обзор изменений
Реализована система ленивой загрузки табов с пагинацией. Теперь список табов загружается сразу при раскрытии строки, но содержимое каждого таба загружается только при его открытии.

### Эндпойнты, которые работают как ленивые табы
В коде настроены следующие эндпойнты для ленивой загрузки:
- `krg3_input_package_def` - основная таблица пакетов
- `krg3_request_notice` - основная таблица заявок

### 1. Основные эндпойнты (уже существующие, требуют модификации)

#### `POST /krg3_input_package_def`
**Назначение**: Загрузка основной таблицы пакетов с информацией о табах

**Параметры запроса**:
```json
{
  "cabinetId": "string",
  "pageSize": 10,
  "pageNumber": 1
}
```

**Структура ответа** (ИЗМЕНЕНА):
```json
{
  "columns": [
    {
      "title": "Номер",
      "dataIndex": "number",
      "key": "number",
      "columnType": "String",
      "width": 100,
      "align": "left",
      "columnUuid": null,
      "filterType": null,
      "filters": null,
      "sortable": false
    }
  ],
  "rows": [
    {
      "number": 1,
      "status": "Принят в КРГ",
      "dateRegOrDissSadd": "15.10.2022 11:22:10",
      "id": "701d1479-05d5-4f61-a13d-de3acce42ce3",
      "key": "701d1479-05d5-4f61-a13d-de3acce42ce3",
      "nestedTable": {
        "tabs": [
          {
            "label": "Файлы в составе пакета",
            "key": "1",
            "endpoint": "krg3_package_files_def",
            "tableData": null
          }
        ]
      },
      "rowId": {
        "id": "701d1479-05d5-4f61-a13d-de3acce42ce3",
        "hasNested": true
      }
    }
  ],
  "pagination": {
    "total": 3,
    "pageSize": 10
  },
  "pageNumber": 1,
  "sort": null,
  "group": null
}
```

**Ключевые изменения**:
- В `nestedTable.tabs[].tableData` должно быть `null` (данные загружаются лениво)
- Поле `endpoint` указывает на эндпойнт для загрузки содержимого таба

#### `POST /krg3_request_notice`
**Назначение**: Загрузка основной таблицы заявок с информацией о табах

**Параметры и структура ответа**: Аналогично `krg3_input_package_def`, но с соответствующими полями для заявок.

### 2. Эндпойнты для загрузки содержимого табов

#### `POST /krg3_package_files_def`
**Назначение**: Загрузка содержимого таба "Файлы в составе пакета" с пагинацией

**Параметры запроса**:
```json
{
  "cabinetId": "string",
  "packageDefId": "string",
  "pageSize": 10,
  "pageNumber": 1
}
```

**Структура ответа**:
```json
{
  "columns": [
    {
      "title": "Имя",
      "dataIndex": "name",
      "key": "name",
      "columnType": "String",
      "width": 300,
      "align": "left",
      "columnUuid": null,
      "filterType": null,
      "filters": null,
      "sortable": false
    },
    {
      "title": "Расположение файла",
      "dataIndex": "cabinet",
      "key": "cabinet",
      "columnType": "String",
      "width": 600,
      "align": "left"
    }
  ],
  "rows": [
    {
      "name": "document.pdf",
      "cabinet": "/Пакет 2",
      "size": 125.67,
      "dateUpload": "20.12.2022 10:15:30",
      "key": "b7c54bc8-2d58-45be-bb55-ecdebb664710",
      "rowId": {
        "id": "b7c54bc8-2d58-45be-bb55-ecdebb664710",
        "canView": true,
        "canDownload": true,
        "format": "pdf"
      }
    }
  ],
  "pagination": {
    "total": 25,
    "pageSize": 10
  },
  "pageNumber": 1,
  "sort": null,
  "group": null
}
```

#### `POST /krg3_request_item`
**Назначение**: Загрузка содержимого таба "Пункты заявки" с пагинацией

**Параметры запроса**:
```json
{
  "cabinetId": "string",
  "requestId": "string",
  "pageSize": 10,
  "pageNumber": 1
}
```

**Структура ответа**: Аналогично другим табам, но с полями специфичными для пунктов заявки.

#### `POST /krg3_notice_for_request`
**Назначение**: Загрузка содержимого таба "Уведомления, связанные с заявкой" с пагинацией

**Параметры запроса**:
```json
{
  "cabinetId": "string",
  "requestId": "string",
  "pageSize": 10,
  "pageNumber": 1
}
```

### 3. Важные особенности реализации

#### Структура nestedTable для ленивых табов
```typescript
{
  "nestedTable": {
    "tabs": [
      {
        "label": "Название таба",
        "key": "уникальный_ключ",
        "endpoint": "имя_эндпойнта_для_загрузки_содержимого",
        "tableData": null  // ВСЕГДА null при первоначальной загрузке
      }
    ]
  }
}
```

#### Пагинация
- Каждый таб имеет собственную пагинацию
- Параметр `pageSize` по умолчанию = 10
- Параметр `pageNumber` начинается с 1
- В ответе обязательно поле `pagination.total` для корректной работы пагинации

#### Идентификация записей
- Для связи основной записи с содержимым табов используются ID из `rowId.id`
- Эти ID передаются как параметры в запросы к эндпойнтам табов (например, `packageDefId`, `requestId`)

### 4. Последовательность запросов

1. **Загрузка основной таблицы**: `POST /krg3_input_package_def`
   - Возвращает строки с `nestedTable.tabs[].tableData = null`
   - **Список табов уже включен в ответ, отдельный запрос НЕ выполняется**

2. **Раскрытие строки**: Пользователь раскрывает строку
   - Показываются табы из `nestedTable.tabs` (уже загруженные)

3. **Открытие таба**: Пользователь кликает на таб
   - Выполняется запрос к эндпойнту из `tabs[].endpoint`
   - Загружается первая страница данных

4. **Переключение страниц**: Пользователь меняет страницу в пагинации
   - Выполняется новый запрос с обновленным `pageNumber`

### 5. Обработка ошибок

Эндпойнты должны возвращать стандартные HTTP коды ошибок:
- `200` - успешная загрузка
- `400` - неверные параметры запроса
- `404` - данные не найдены
- `500` - внутренняя ошибка сервера

## Следующие шаги

1. Протестировать на реальных данных
2. Добавить обработку ошибок загрузки
3. Оптимизировать производительность при большом количестве табов
4. Добавить кэширование загруженных данных
