# Руководство по тестированию ленивых табов с пагинацией

## Обзор реализации

Была реализована система ленивой загрузки табов с пагинацией для NewWorkGroupTabs. Теперь:

1. **Список табов загружается сразу** при раскрытии строки с вложенной таблицей
2. **Содержимое каждого таба загружается лениво** только при открытии конкретного таба
3. **Каждый таб имеет собственную пагинацию** с сохранением состояния

## Что было изменено

### Новые типы
- `NestedTabsWithLazyTable` - структура для ленивых табов
- `TabsWithLazyTable` - отдельный таб с ленивой загрузкой и пагинацией
- `CollapseWithLazyTableProps` - пропсы для компонента ленивых табов

### Новые компоненты
- `CollapseWithLazyTable` - компонент для отображения ленивых табов
- `tableConfigWithLazyTabs` - конфигурация таблицы для ленивых табов

### Обновленные хуки
- `useNestedData` - добавлена функция `getLazyTabContent` для загрузки содержимого табов

## Как тестировать

### 1. Настройка эндпойнта
Эндпойнт `apiUrls.workGroup.nestedTabsEndpoints.packageDef` настроен как ленивый.

### 2. Ожидаемое поведение

#### При раскрытии строки:
- Показывается список табов (например, "Файлы в составе пакета")
- Содержимое табов НЕ загружается сразу

#### При открытии таба:
- Показывается спиннер загрузки
- Выполняется запрос к эндпойнту таба (например, `krg3_package_files_def`)
- Загружается первая страница данных
- Показывается таблица с данными и пагинация (если данных больше pageSize)

#### При переключении страниц:
- Выполняется новый запрос с параметром `pageNumber`
- Обновляется содержимое таба
- Состояние пагинации сохраняется для каждого таба отдельно

### 3. Структура запросов

#### Запрос списка табов:
```
POST /krg3_input_package_def/tabs
{
  cabinetId: "...",
  packageDefId: "..."
}
```

#### Запрос содержимого таба:
```
POST /krg3_package_files_def
{
  cabinetId: "...",
  packageDefId: "...",
  pageSize: 10,
  pageNumber: 1
}
```

### 4. Проверка в DevTools

1. Откройте Network tab в DevTools
2. Раскройте строку с packageDef
3. Проверьте, что выполняется запрос `/tabs`
4. Откройте таб "Файлы в составе пакета"
5. Проверьте, что выполняется запрос к эндпойнту таба
6. Переключите страницу в пагинации
7. Проверьте, что выполняется новый запрос с обновленным pageNumber

## Возможные проблемы

1. **Эндпойнт не настроен как ленивый** - проверьте `lazyNestedTableEndpoints` в `useNestedData`
2. **Неправильная структура ответа** - убедитесь, что бэкенд возвращает правильную структуру
3. **Ошибки загрузки** - проверьте консоль на наличие ошибок сети или JavaScript

## Следующие шаги

1. Протестировать на реальных данных
2. Добавить обработку ошибок загрузки
3. Оптимизировать производительность при большом количестве табов
4. Добавить кэширование загруженных данных
